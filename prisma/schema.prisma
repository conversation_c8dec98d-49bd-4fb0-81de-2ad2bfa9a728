generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id String @id @default(uuid())
  name String @default("")
  email String @unique
  password String
  number String?
  billingAddress String?
  billingPostal String?
  billingCity String? 
  billingCountry String? 
  shippingAddress String?
  shippingPostal String? 
  shippingCity String?
  shippingCountry String?
  createdAt DateTime @default(now())
  updatedAt DateTime? @updatedAt 
  resetToken String?
  }

model Transaction {
  id          String   @id @default(uuid())
  transactionId String
  userId       String
  externalUserId String
  qrCodeId    String
  transactionType String
  status       String
  amount      Float
  currency    String
  companyName String
  redirectUrl String
  returnUrl   String
  emailSent Boolean @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
