import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { CreateTransactionDto } from '../dto/create-transaction.dto';
import { UpdateTransactionDto } from '../dto/update-transaction.dto';

@Injectable()
export class TransactionsService {
  constructor(private readonly prisma: DatabaseService) {}

  async createTransaction(dto: CreateTransactionDto) {
    return await this.prisma.transaction.create({
      data: dto,
    });
  }

  async getAllTransactions(
    userId: string, 
    page: number, 
    perPage: number = 20, 
    startDate?: string, 
    endDate?: string,
    status?: string,
  ) {
    if (page === undefined || isNaN(page) || page < 1) {
      throw new BadRequestException({
        statusCode: 400,
        message: `Page number: ${page}. Page must be at least 1.`,
        error: 'Bad Request',
      });
    }

    if (!userId) {
      throw new BadRequestException({
        statusCode: 400,
        message: `User ID is required.`,
        error: 'Bad Request',
      });
    }

    const skip = (page - 1) * perPage;
    
    const whereCondition: any = {
      userId: userId,
    };

    if (startDate && endDate) {
      whereCondition.createdAt = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    }

    if (status && ['success', 'failed'].includes(status)) {
      whereCondition.status = status;
    } else {
        whereCondition.status = {
            in: ['success', 'failed'],
        };
    }
    
    const transactions = await this.prisma.transaction.findMany({
      where: whereCondition,
      skip,
      take: perPage,
      orderBy: { createdAt: 'desc' },
    });

    const totalCount = await this.prisma.transaction.count({ where: whereCondition });

    return {
      totalCount,
      page,
      perPage,
      totalPages: Math.ceil(totalCount / perPage),
      transactions,
    };
  }

  async getTransactionById(id: string) {
    const transaction = await this.prisma.transaction.findUnique({
      where: { id },
    });
    if (!transaction) {
      throw new NotFoundException('Transaction not found');
    }
    return transaction;
  }

  async updateTransaction(id: string, dto: UpdateTransactionDto) {
    const transaction = await this.prisma.transaction.findUnique({
      where: { id },
    });
  
    if (!transaction) {
      throw new NotFoundException(`Transaction with ID ${id} not found`);
    }
  
    return await this.prisma.transaction.update({
      where: { id },
      data: dto,
    });
  }
}
