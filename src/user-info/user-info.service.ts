import { Injectable, NotFoundException } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { UpdateUserInfoDto } from '../dto/update-user-info.dto';

@Injectable()
export class UserInfoService {
  constructor(private database: DatabaseService) {}

  async updateUser(userId: string, updateUserInfoDto: UpdateUserInfoDto) {
    const user = await this.database.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    await this.database.user.update({
      where: { id: userId },
      data: {
        name: updateUserInfoDto.name ?? "", 
        email: updateUserInfoDto.email ?? user.email, 
        number: updateUserInfoDto.number ?? null, 
        billingAddress: updateUserInfoDto.billingAddress ?? user.billingAddress,
        billingPostal: updateUserInfoDto.billingPostal ?? user.billingPostal,
        billingCity: updateUserInfoDto.billingCity ?? user.billingCity,
        billingCountry: updateUserInfoDto.billingCountry ?? user.billingCountry,
        shippingAddress: updateUserInfoDto.shippingAddress ?? user.shippingAddress,
        shippingPostal: updateUserInfoDto.shippingPostal ?? user.shippingPostal,
        shippingCity: updateUserInfoDto.shippingCity ?? user.shippingCity,
        shippingCountry: updateUserInfoDto.shippingCountry ?? user.shippingCountry,
      },
    });
    return {
      status: true,
      message: "Successfully saved",
      data: null
    };
  }
  async getUserById(userId: string) {
    const user = await this.database.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        number: true,
        billingAddress: true, 
        billingPostal: true, 
        billingCity: true, 
        billingCountry: true, 
        shippingAddress: true, 
        shippingPostal: true, 
        shippingCity: true, 
        shippingCountry: true, 
      }, 
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return {
      status: true,
      message: "User data retrieved successfully",
      data: user,
    };
  }
}
