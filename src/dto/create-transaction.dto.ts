import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>NotEmpty } from 'class-validator';

export class CreateTransactionDto {
  @IsString()
  @IsNotEmpty()
  transactionId: string;

  @IsString()
  @IsNotEmpty()
  userId: string;

  @IsString()
  @IsNotEmpty()
  externalUserId: string;

  @IsString()
  @IsNotEmpty()
  transactionType: string;

  @IsString()
  @IsNotEmpty()
  qrCodeId: string;

  @IsString()
  @IsNotEmpty() 
  status: string;

  @IsNumber()
  amount: number;

  @IsString()
  @IsNotEmpty()
  currency: string;

  @IsString()
  @IsNotEmpty()
  companyName: string;

  @IsString()
  @IsNotEmpty()
  redirectUrl: string;

  @IsString()
  @IsNotEmpty()
  returnUrl: string;
}

