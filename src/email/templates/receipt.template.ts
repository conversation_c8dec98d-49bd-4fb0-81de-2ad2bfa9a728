export const generateReceiptTemplate = (
  transaction: any,
  userEmail?: string,
  userName?: string,
) => {
  const formattedDate = new Date(transaction.createdAt).toLocaleString();
  return `
    <div style="font-family: 'Segoe UI', Roboto, sans-serif; background-color: #f4f6f8; padding: 20px;">
      <div style="max-width: 600px; margin: auto; background-color: #ffffff; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.05); padding: 24px;">
        
        <!-- Logo -->
        <div style="text-align: center; margin-bottom: 16px;">
          <img src="https://www.hazelsone.com/assets/hazelsonelogo.png" alt="Hazel Pay" style="height: 56px;" />
        </div>

        <!-- Gray Line -->
        <hr style="border: none; border-top: 1px solid #ccc;" />

        <!-- Greeting -->
        <div style="margin: 24px 0 16px;">
          <h2 style="font-size: 20px; font-weight: 600; color: #333; margin-bottom: 6px;">Hi, ${userName || 'User'}!</h2>
          <p style="font-size: 14px; color: #555;">You have a new transaction on Hazel Pay.</p>
        </div>

        <!-- Gray Line -->
        <hr style="border: none; border-top: 1px solid #ccc;" />

        <!-- Transaction Details -->
  <div style="margin: 24px 0;">
  <h3 style="font-size: 20px; font-weight: 600; color: #333; margin-bottom: 10px;">Transaction Details</h3>
  
  <div class="detail-row" style="
  display: flex; 
  margin: 8px 0; 
  justify-content: space-between; 
  align-items: flex-start;">
    <span style="font-size: 14px; 
    color: #333; 
    font-weight: 600; 
    min-width: 25vw; 
    max-width: 160px;">ID:</span>
    <span style="
      font-size: 14px;
      color: #333;
      max-width: calc(75vw - 150px);
      display: inline-block;
      text-align: right;
      word-break: break-all;
    ">${transaction.id}</span>
  </div>

  <div class="detail-row" style="display: flex; margin: 8px 0; justify-content: space-between; align-items: flex-start;">
    <span style="font-size: 14px; color: #333; font-weight: 600; min-width: 25vw; max-width: 160px;">Time:</span>
    <span style="font-size: 14px; color: #333; text-align: right;">${formattedDate}</span>
  </div>
</div>

        <!-- Amount Details -->
        <div style="margin: 24px 0;">
          <h3 style="font-size: 20px; font-weight: 600; color: #333; margin-bottom: 10px;">Amount Details</h3>
          <div class="detail-row" style="display: flex; margin: 8px 0; justify-content: space-between; align-items: flex-start;">
            <span style="font-size: 14px; color: #333; font-weight: 600; min-width: 25vw; max-width: 160px;">Amount:</span>
            <span style="font-size: 14px; color: #333; text-align: right;">${transaction.amount}</span>
          </div>
          <div class="detail-row" style="display: flex; margin: 8px 0; justify-content: space-between; align-items: flex-start;">
            <span style="font-size: 14px; color: #333; font-weight: 600; min-width: 25vw; max-width: 160px;">Currency:</span>
            <span style="font-size: 14px; color: #333; text-align: right;">${transaction.currency}</span>
          </div>
        </div>

        <!-- Additional Info -->
        <div style="margin: 24px 0;">
          <h3 style="font-size: 20px; font-weight: 600; color: #333; margin-bottom: 10px;">Additional Information</h3>
          ${userName ? `
            <div class="detail-row" style="display: flex; margin: 8px 0; justify-content: space-between; align-items: flex-start;">
              <span style="font-size: 14px; color: #333; font-weight: 600; min-width: 25vw; max-width: 160px;">Sender:</span>
              <span style="font-size: 14px; color: #333; text-align: right;">${userName}</span>
            </div>` : ''
          }
          ${userEmail ? `
            <div class="detail-row" style="display: flex; margin: 8px 0; justify-content: space-between; align-items: flex-start;">
              <span style="font-size: 14px; color: #333; font-weight: 600; min-width: 25vw; max-width: 160px;">Sender Email:</span>
              <span style="font-size: 14px; color: #333; text-align: right;">${userEmail}</span>
            </div>` : ''
          }
          <div class="detail-row" style="display: flex; margin: 8px 0; justify-content: space-between; align-items: flex-start;">
            <span style="font-size: 14px; color: #333; font-weight: 600; min-width: 25vw; max-width: 160px;">Recipient:</span>
            <span style="font-size: 14px; color: #333; text-align: right;">${transaction.companyName}</span>
          </div>
          <div class="detail-row" style="display: flex; margin: 8px 0; justify-content: space-between; align-items: flex-start;">
            <span style="font-size: 14px; color: #333; font-weight: 600; min-width: 25vw; max-width: 160px;">Status:</span>
            <span style="font-size: 14px; color: #333; text-align: right;">${transaction.status.toUpperCase()}</span>
          </div>
        </div>

        <!-- Final Divider -->
        <hr style="border: none; border-top: 1px solid #ccc;" />

        <!-- Footer Info -->
        <p style="font-size: 13px; color: #777; text-align: center; margin-top: 16px;">
          Details of this transaction can be viewed on the Hazel Pay app.
        </p>
        <p style="text-align: center; font-size: 13px; color: #444;">Thank you for using Hazel Pay!</p>
      </div>
      <style>
        @media screen and (max-width: 480px) {
          .detail-row span:first-child {
            min-width: 35vw !important;
            max-width: 120px !important;
          }
          .detail-row span:last-child {
            text-align: right;
          }
        }
        @media screen and (min-width: 481px) and (max-width: 768px) {
          .detail-row span:first-child {
            min-width: 30vw !important;
            max-width: 140px !important;
          }
          .detail-row span:last-child {
            text-align: right;
          }
        }
      </style>
    </div>
  `;
};